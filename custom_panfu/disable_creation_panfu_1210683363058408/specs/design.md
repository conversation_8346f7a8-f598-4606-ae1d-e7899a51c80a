# 技术方案设计

## 技术架构与实现思路

- 通过继承和扩展 Odoo 相关模型和视图，针对 Master Product 字段和 Vendor 字段，禁用 many2one 字段的 create/edit 快捷入口（即 create/edit=true 改为 false）。
- 若字段为 many2one，需在 XML 视图中设置 options="{'no_create': True, 'no_create_edit': True}"。
- 若有自定义小部件或 JS 端自动创建逻辑，也需同步屏蔽。
- 兼容多语言提示，确保用户体验。

## 涉及模块
- 产品（product.product 或相关自定义字段）
- 采购（purchase.order 或相关 Vendor 字段）

## 技术选型
- Odoo 17 原生继承机制
- 仅后端 XML/py，无需前端 JS（如有特殊小部件再补充）

## 数据库/接口
- 无需新增表，仅扩展视图和模型

## 测试策略
- 单元测试：尝试输入不存在的 Master Product/Vendor，验证无法创建
- 界面测试：验证下拉选择器无“创建并编辑”入口
- 多语言测试：提示信息友好

```mermaid
graph TD;
    用户--->界面下拉选择器--->仅可选已有记录
    用户--X--->自动创建新记录
``` 