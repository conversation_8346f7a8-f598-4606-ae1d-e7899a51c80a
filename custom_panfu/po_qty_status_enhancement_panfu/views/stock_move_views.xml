<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 自定义stock.move视图 -->
    <record id="view_stock_move_tree_po_enhanced" model="ir.ui.view">
        <field name="name">stock.move.tree.po.enhanced</field>
        <field name="model">stock.move</field>
        <field name="arch" type="xml">
            <tree string="Pending PO Moves" create="0" edit="1">
                <field name="reference"/>
                <field name="product_id"/>
                <field name="product_uom_qty"/>
                <field name="product_uom"/>
                <field name="state" widget="badge" optional="show" decoration-danger="state=='cancel'" decoration-info="state== 'assigned'" decoration-muted="state == 'draft'" decoration-success="state == 'done'" decoration-warning="state not in ('draft','cancel','done','assigned')"/>
                <field name="location_id"/>
                <field name="location_dest_id"/>
                <field name="picking_id"/>
                <field name="origin"/>
            </tree>
        </field>
    </record>
    
    <!-- 自定义action使用新视图 -->
    <record id="action_stock_move_po_enhanced" model="ir.actions.act_window">
        <field name="name">Pending PO Moves</field>
        <field name="res_model">stock.move</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>