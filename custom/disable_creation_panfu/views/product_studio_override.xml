<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- 继承 Odoo Studio 自定义视图，优先级设为 100，高于 Studio 的 99 -->
        <record id="product_template_studio_override_no_create_panfu" model="ir.ui.view">
            <field name="name">product.template.studio.override.nocreate.panfu</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="studio_customization.odoo_studio_product__95857359-f949-4dd2-802a-e631a5ce4f15"/>
            <field name="priority">100</field>
            <field name="arch" type="xml">
                <!-- 修改树视图中的 partner_id 字段 -->
                <xpath expr="//field[@name='seller_ids']//tree//field[@name='partner_id']" position="attributes">
                    <attribute name="options">{'no_create': True, 'no_create_edit': True}</attribute>
                </xpath>
                
                <!-- 修改表单视图中的 partner_id 字段 -->
                <xpath expr="//field[@name='seller_ids']//form//field[@name='partner_id']" position="attributes">
                    <attribute name="options">{'no_create': True, 'no_create_edit': True}</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo> 